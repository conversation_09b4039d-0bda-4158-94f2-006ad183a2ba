import { getPriceString } from '@/utils';
import { OrderHistoryDetailVendorOrderType } from '@/libs/orders/types';

type VendorOrderSummaryProps = Omit<OrderHistoryDetailVendorOrderType, 'id'> & {
  showDivider?: boolean;
};

export const VendorOrderSummary = ({
  vendor,
  items,
  totalTaxFee,
  shippingFee,
  totalPrice,
  showDivider = false,
}: VendorOrderSummaryProps) => {
  return (
    <div>
      {showDivider && <hr className="my-4 border-t border-gray-200" />}
      <div className="flex">
        <div className="flex-1">
          <p className="text-gray-600">Vendor</p>
          <p className="font-medium text-gray-800">{vendor.name}</p>
        </div>
        <div
          className="mx-4 w-px self-stretch bg-gray-200"
          aria-hidden="true"
        />
        <div className="flex-1">
          <p className="text-gray-600">Line items</p>
          <p className="font-medium text-gray-800">{items.length}</p>
        </div>
        <div
          className="mx-4 w-px self-stretch bg-gray-200"
          aria-hidden="true"
        />
        <div className="flex-1">
          <p className="text-gray-600">Taxes</p>
          <p className="font-medium text-gray-800">
            {totalTaxFee ? getPriceString(+totalTaxFee) : '–'}
          </p>
        </div>
        <div
          className="mx-4 w-px self-stretch bg-gray-200"
          aria-hidden="true"
        />
        <div className="flex-1">
          <p className="text-gray-600">Shipping</p>
          <p className="font-medium text-gray-800">
            {shippingFee ? getPriceString(+shippingFee) : '–'}
          </p>
        </div>
        <div
          className="mx-4 w-px self-stretch bg-gray-200"
          aria-hidden="true"
        />
        <div className="flex-1">
          <p className="text-gray-600">Vendor total</p>
          <p className="font-medium text-gray-800">
            {getPriceString(+totalPrice)}
          </p>
        </div>
      </div>
    </div>
  );
};
