import { FEATURE_FLAGS } from '@/constants';
import { OrderStatus } from '../../../OrderStatus/OrderStatus';
import { ProductCartHorizontal } from '@/libs/products/components/ProductCardHorizontal/ProductCartHorizontal';
import { But<PERSON> } from '@/libs/ui/Button/Button';
import PlusIcon from '@/assets/images/plus.svg?react';
import type { OrderHistoryDetailItemType } from '@/libs/orders/types';
import { OrderHistoryItemContent } from '../OrderHistoryItemContent/OrderHistoryItemContent';
import { Icon } from '@/libs/icons/Icon';

interface OrderHistoryRegularItemProps {
  item: OrderHistoryDetailItemType;
}

export const OrderHistoryRegularItem = ({
  item,
}: OrderHistoryRegularItemProps) => {
  return (
    <div key={item.id}>
      <ProductCartHorizontal
        product={item.product}
        productOfferId={item.productOfferId || ''}
        content={<OrderHistoryItemContent item={item} />}
        actions={
          <div className="text-right">
            <p className="mb-1 text-xs font-medium text-gray-500/70">Status</p>
            {item.status && <OrderStatus status={item.status} align="right" />}
            {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
              <Button className="mt-2">
                <Icon name="cartSummary" size={'1.3rem'} />
                <PlusIcon />
              </Button>
            )}
          </div>
        }
      />
    </div>
  );
};
