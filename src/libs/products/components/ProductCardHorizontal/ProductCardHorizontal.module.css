.imageContainer {
  border-radius: 0.5rem;
  background-color: #fff;
  border: 1px solid #f2f2f2;
  position: relative;
  width: 240px;
  height: 130px;
  padding: 0 4rem 100%;
  overflow: hidden;
  padding: 0;

  img {
    position: absolute;
    width: 100%;
    left: 50%;
    transform: translate(-50%, -50%);
    top: calc(50% + 1rem);
    max-width: calc(100% - 2rem);
    max-height: calc(100% - 2rem);
    object-fit: scale-down;
  }

  img[data-fallback='true'] {
    top: 50%;
  }
}
