import { OfferType } from '@/types';
import { Stack } from '@mantine/core';
import { SuggestedOfferItem } from './SuggestedOfferItem';

export type SuggestedOfferListProps = {
  offers: OfferType[];
};

export const SuggestedOfferList = ({
  offers = [],
}: SuggestedOfferListProps) => {
  return (
    <Stack gap={8}>
      {offers.map((offer) => (
        <SuggestedOfferItem
          key={offer.id}
          offer={offer}
          showOriginalPrice={false}
        />
      ))}
    </Stack>
  );
};
