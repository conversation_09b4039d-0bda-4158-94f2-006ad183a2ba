import type { ReactNode } from 'react';
import { Text } from '@mantine/core';

interface TopNavbarProps {
  title?: string;
  children?: ReactNode;
}
export const TopNavbar = ({ title, children }: TopNavbarProps) => {
  return (
    <div className="space-between flex min-h-[75px] w-full min-w-1/2 items-center gap-5 border-b border-gray-200 bg-white px-6 py-4">
      {title ? (
        <Text size="1.25rem" fw="700" mr="0.75rem">
          {title}
        </Text>
      ) : null}
      {children}
    </div>
  );
};
