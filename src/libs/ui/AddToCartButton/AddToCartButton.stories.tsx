import type { Meta, StoryObj } from '@storybook/react-vite';
import { Flex } from '@/libs/ui/Flex/Flex';
import { AddToCartButton } from './AddToCartButton';

const meta: Meta<typeof AddToCartButton> = {
  title: 'Product/AddToCartButton',
  component: AddToCartButton,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    height: {
      control: { type: 'select' },
      options: ['sm', 'normal'],
    },
    isLoading: {
      control: { type: 'boolean' },
    },
    isDisabled: {
      control: { type: 'boolean' },
    },
    onClick: {
      action: 'clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Normal: Story = {
  args: {
    height: 'normal',
    isLoading: false,
    isDisabled: false,
  },
};

export const Small: Story = {
  args: {
    height: 'sm',
    isLoading: false,
    isDisabled: false,
  },
};

export const Loading: Story = {
  args: {
    height: 'normal',
    isLoading: true,
    isDisabled: false,
  },
};

export const Disabled: Story = {
  args: {
    height: 'normal',
    isLoading: false,
    isDisabled: true,
  },
};

export const ResponsiveBehavior: Story = {
  render: () => (
    <Flex direction="column" gap="lg">
      <div>
        <h3 style={{ marginBottom: '8px' }}>
          Narrow container (shows icon only)
        </h3>
        <div
          style={{ width: '80px', border: '1px dashed #ccc', padding: '8px' }}
        >
          <AddToCartButton height="normal" />
        </div>
      </div>
      <div>
        <h3 style={{ marginBottom: '8px' }}>Wide container (shows text)</h3>
        <div
          style={{ width: '200px', border: '1px dashed #ccc', padding: '8px' }}
        >
          <AddToCartButton height="normal" />
        </div>
      </div>
    </Flex>
  ),
};

export const HeightComparison: Story = {
  render: () => (
    <Flex gap="md" align="center">
      <div>
        <h4 style={{ marginBottom: '8px' }}>Normal Height</h4>
        <div style={{ width: '150px' }}>
          <AddToCartButton height="normal" />
        </div>
      </div>
      <div>
        <h4 style={{ marginBottom: '8px' }}>Small Height</h4>
        <div style={{ width: '150px' }}>
          <AddToCartButton height="sm" />
        </div>
      </div>
    </Flex>
  ),
};
