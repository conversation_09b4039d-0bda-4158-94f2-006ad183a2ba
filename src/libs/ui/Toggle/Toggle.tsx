import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { mergeClasses } from '@/utils/tailwind';

const toggleVariants = cva(
  'relative inline-flex items-center rounded-full transition-colors focus:ring-2 focus:ring-offset-2 focus:outline-none',
  {
    variants: {
      size: {
        sm: 'h-4 w-7',
        md: 'h-6 w-11',
        lg: 'h-8 w-14',
      },
      state: {
        enabled:
          'cursor-pointer bg-[var(--mantine-color-blue-6)] focus:ring-[var(--mantine-color-blue-6)]',
        disabled:
          'cursor-not-allowed opacity-50 bg-[var(--mantine-color-dark-4)] focus:ring-[var(--mantine-color-dark-4)]',
      },
    },
    defaultVariants: {
      size: 'md',
      state: 'disabled',
    },
  },
);

const toggleThumbVariants = cva(
  'inline-block transform rounded-full bg-white shadow-sm transition-transform',
  {
    variants: {
      size: {
        sm: 'h-3 w-3',
        md: 'h-4 w-4',
        lg: 'h-6 w-6',
      },
      position: {
        enabled: 'translate-x-6',
        disabled: 'translate-x-1',
      },
    },
    compoundVariants: [
      {
        size: 'sm',
        position: 'enabled',
        class: 'translate-x-3.5',
      },
      {
        size: 'lg',
        position: 'enabled',
        class: 'translate-x-7',
      },
      {
        size: 'sm',
        position: 'disabled',
        class: 'translate-x-0.5',
      },
    ],
    defaultVariants: {
      size: 'md',
      position: 'disabled',
    },
  },
);

interface ToggleProps extends VariantProps<typeof toggleVariants> {
  enabled: boolean;
  onToggle: (enabled: boolean) => void;
  disabled?: boolean;
  className?: string;
  'aria-label'?: string;
}

export const Toggle = ({
  enabled,
  onToggle,
  disabled = false,
  size = 'md',
  className = '',
  'aria-label': ariaLabel,
}: ToggleProps) => {
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (disabled) return;

    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onToggle(!enabled);
    }
  };

  return (
    <button
      type="button"
      role="switch"
      aria-checked={enabled}
      aria-label={ariaLabel || `Toggle ${enabled ? 'on' : 'off'}`}
      aria-pressed={enabled}
      onClick={() => !disabled && onToggle(!enabled)}
      onKeyDown={handleKeyDown}
      disabled={disabled}
      tabIndex={disabled ? -1 : 0}
      className={mergeClasses(
        toggleVariants({
          size,
          state: enabled ? 'enabled' : 'disabled',
        }),
        className,
      )}
    >
      <span
        className={toggleThumbVariants({
          size,
          position: enabled ? 'enabled' : 'disabled',
        })}
      />
    </button>
  );
};
