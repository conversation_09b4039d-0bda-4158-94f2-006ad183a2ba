import { useRef, useEffect, type MouseEvent, type ReactNode } from 'react';
import { useComboboxContext } from '../../ComboboxContext';
import { mergeClasses } from '@/utils';

interface ComboboxOptionProps {
  value: unknown;
  children: ReactNode;
  className?: string;
  disabled?: boolean;
}

export function ComboboxOption({
  value,
  children,
  className,
  disabled = false,
}: ComboboxOptionProps) {
  const {
    selectOption,
    registerOption,
    unregisterOption,
    getOptionValue,
    highlightedIndex,
    optionsRefsKeys,
  } = useComboboxContext();

  const optionRef = useRef<HTMLDivElement>(null);

  const isHighlighted = (() => {
    if (highlightedIndex < 0 || highlightedIndex >= optionsRefsKeys.length) {
      return false;
    }

    const currentIndex = optionsRefsKeys.findIndex((option) => {
      if (!getOptionValue) {
        return option === value;
      }
      try {
        return getOptionValue(option) === getOptionValue(value);
      } catch {
        return option === value;
      }
    });

    return currentIndex === highlightedIndex && currentIndex >= 0;
  })();

  useEffect(() => {
    registerOption(value, optionRef.current);
    return () => unregisterOption(value);
  }, [value, registerOption, unregisterOption]);

  useEffect(() => {
    if (isHighlighted && optionRef.current) {
      optionRef.current.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth',
      });
    }
  }, [isHighlighted]);

  const handleClick = (event: MouseEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (!disabled) {
      selectOption(value);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (!disabled) {
        selectOption(value);
      }
    }
  };

  return (
    <div
      ref={optionRef}
      className={mergeClasses(
        'cursor-pointer rounded px-3 py-2 break-words transition-colors duration-150 ease-in-out select-none',
        'focus:ring-opacity-50 hover:text-blue-700 focus:text-blue-700 focus:ring-2 focus:outline-none',
        isHighlighted && 'text-blue-700',
        disabled && 'cursor-not-allowed opacity-50',
        className,
      )}
      style={
        {
          '--color-yellow-300': 'oklch(90.5% 0.182 98.111)',
          '--tw-ring-color': 'var(--color-yellow-300)',
        } as React.CSSProperties
      }
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={disabled ? -1 : 0}
      role="option"
      aria-selected={isHighlighted}
      aria-disabled={disabled}
      data-highlighted={isHighlighted}
      data-disabled={disabled}
    >
      {children}
    </div>
  );
}
