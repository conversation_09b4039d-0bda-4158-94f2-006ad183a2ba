import { PromoType } from '@/types/common';
import { getMinimumQuantity } from './getMinimumQuantity';
import { getFreeBenefit } from './getFreeBenefit';

type FreeItemsQtyProps = {
  benefits: PromoType['benefits'];
  requirements: PromoType['requirements'];
  totalCartQuantity: number;
};

export function getFreeItemsQty({
  benefits,
  requirements,
  totalCartQuantity,
}: FreeItemsQtyProps): number {
  const minimumQuantity = getMinimumQuantity(requirements);
  const freeBenefit = getFreeBenefit(benefits);

  if (!freeBenefit) return 0;

  const triggers = Math.floor(totalCartQuantity / minimumQuantity);
  return triggers * freeBenefit.quantity;
}
